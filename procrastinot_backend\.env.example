# ==============================================
# PROCRASTINOT BACKEND ENVIRONMENT VARIABLES
# ==============================================
# Copy this file to .env and fill in your actual values

# ==============================================
# DATABASE CONFIGURATION
# ==============================================
# MongoDB connection string
# Example: mongodb://localhost:27017/procrastinot
# Or MongoDB Atlas: mongodb+srv://username:<EMAIL>/procrastinot
MONGO_URI=mongodb+srv://chaithuregala123:<EMAIL>/

# ==============================================
# SERVER CONFIGURATION
# ==============================================
# Port for the backend server
PORT=8080

# Frontend URL (for CORS and redirects)
# Development: http://localhost:5173
# Production: https://your-frontend-domain.com
CLIENT_URL=http://localhost:5173

# Backend server URL (for OAuth callbacks)
# Development: http://localhost:8080
# Production: https://your-backend-domain.com
SERVER_URL=http://localhost:8080

# ==============================================
# AUTHENTICATION & SECURITY
# ==============================================
# JWT Secret Key - Use a strong, random string
# Generate with: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
JWT_SECRET=901b7d464709883dddc65765a8f2bb36f613941f1f8e7066dab1c495f7118416afa010d253fa29415ca5f6b4b7c3b663502d10f76c4b2dd67928b2a92e799468

# ==============================================
# GOOGLE OAUTH CONFIGURATION
# ==============================================
# Get these from Google Cloud Console:
# 1. Go to https://console.cloud.google.com/
# 2. Create a new project or select existing
# 3. Enable Google+ API
# 4. Create OAuth 2.0 credentials
# 5. Add authorized redirect URIs:
#    - http://localhost:8080/api/users/google/callback (development)
#    - https://your-backend-domain.com/api/users/google/callback (production)

GOOGLE_CLIENT_ID=337279521010-abkp34jmguqki9bapk67uvjp1ej4f8gi.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# ==============================================
# OPTIONAL CONFIGURATION
# ==============================================
# Node environment (development, production, test)
NODE_ENV=development

# Enable debug logging (true/false)
DEBUG=false
