# ==============================================
# PROCRASTINOT FRONTEND ENVIRONMENT VARIABLES
# ==============================================
# Copy this file to .env and fill in your actual values

# ==============================================
# API CONFIGURATION
# ==============================================
# Backend API URL
# Development: http://localhost:8080
# Production: https://your-backend-domain.com
VITE_API_URL=http://localhost:8080

# ==============================================
# GOOGLE OAUTH CONFIGURATION
# ==============================================
# Google OAuth Client ID
# This should be the same Client ID used in the backend
# Get this from Google Cloud Console:
# 1. Go to https://console.cloud.google.com/
# 2. Select your project
# 3. Go to APIs & Services > Credentials
# 4. Copy the Client ID from your OAuth 2.0 client
# 5. Add authorized JavaScript origins:
#    - http://localhost:5173 (development)
#    - https://your-frontend-domain.com (production)

VITE_GOOGLE_CLIENT_ID=337279521010-abkp34jmguqki9bapk67uvjp1ej4f8gi.apps.googleusercontent.com

# ==============================================
# OPTIONAL CONFIGURATION
# ==============================================
# App environment (development, production)
VITE_APP_ENV=development

# Enable debug mode (true/false)
VITE_DEBUG=false

# App version (for display purposes)
VITE_APP_VERSION=1.0.0
